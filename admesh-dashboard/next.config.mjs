/** @type {import('next').NextConfig} */
const nextConfig = {
  // output: "export", // ✅ enables static export

  // Improve SEO by adding trailing slashes to URLs
  trailingSlash: true,

  // Optimize images
  images: {
    formats: ['image/avif', 'image/webp'],
    domains: ['firebasestorage.googleapis.com'],
    minimumCacheTTL: 60,
  },

  // Add headers for security and SEO
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
        ],
      },
    ];
  },

  // Headers for SEO and security
  async headers() {
    return [
      // Default headers for all pages
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Robots-Tag',
            value: 'index, follow, max-image-preview:large, max-snippet:-1',
          },
        ],
      },
      // No-index headers for dashboard and private pages
      {
        source: '/dashboard/:path*',
        headers: [
          {
            key: 'X-Robots-Tag',
            value: 'noindex, nofollow',
          },
        ],
      },
      {
        source: '/api/:path*',
        headers: [
          {
            key: 'X-Robots-Tag',
            value: 'noindex, nofollow',
          },
        ],
      },
      // No-index headers for test pages
      {
        source: '/test-:path*',
        headers: [
          {
            key: 'X-Robots-Tag',
            value: 'noindex, nofollow',
          },
        ],
      },
      {
        source: '/seo-test',
        headers: [
          {
            key: 'X-Robots-Tag',
            value: 'noindex, nofollow',
          },
        ],
      },
    ];
  },

  // Redirects for SEO
  async redirects() {
    return [
      // Redirect from root to brands
      {
        source: '/',
        destination: '/brands',
        permanent: false,
      },
      // Redirect from singular to plural paths
      {
        source: '/brand',
        destination: '/brands',
        permanent: true,
      },
      {
        source: '/agent',
        destination: '/agents',
        permanent: true,
      },
      // Redirect from old URLs to new ones if needed
      {
        source: '/old-page',
        destination: '/new-page',
        permanent: true,
      },
      // Redirect non-www to www (or vice versa)
      {
        source: '/:path*',
        has: [
          {
            type: 'host',
            value: 'useadmesh.com',
          },
        ],
        destination: 'https://www.useadmesh.com/:path*',
        permanent: true,
      },
    ];
  },
};

export default nextConfig;
