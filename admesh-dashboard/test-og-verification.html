<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AdMesh OG Testing</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-url {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
        .preview-link {
            display: inline-block;
            background: #007cba;
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 4px;
            margin: 5px 0;
        }
        .preview-link:hover {
            background: #005a87;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>🎯 AdMesh Open Graph Testing</h1>
    
    <div class="status success">
        ✅ <strong>Status:</strong> All OG images and metadata are properly configured and ready for testing!
    </div>

    <div class="status info">
        📋 <strong>Instructions:</strong> Use the preview links below to test how AdMesh URLs will appear when shared on social media platforms.
    </div>

    <div class="test-section">
        <h2>🏠 Root Page (Brands Content)</h2>
        <div class="test-url">https://www.useadmesh.com/</div>
        <p><strong>Expected:</strong> Shows brands content with root-og.png image</p>
        <a href="https://www.opengraph.xyz/?url=https://www.useadmesh.com/" target="_blank" class="preview-link">
            Test on OpenGraph.xyz
        </a>
        <a href="https://developers.facebook.com/tools/debug/?q=https://www.useadmesh.com/" target="_blank" class="preview-link">
            Test on Facebook Debugger
        </a>
        <a href="https://cards-dev.twitter.com/validator?url=https://www.useadmesh.com/" target="_blank" class="preview-link">
            Test on Twitter Validator
        </a>
    </div>

    <div class="test-section">
        <h2>🤖 Agents Page</h2>
        <div class="test-url">https://www.useadmesh.com/agents</div>
        <p><strong>Expected:</strong> Shows agents content with agents-og.png image</p>
        <a href="https://www.opengraph.xyz/?url=https://www.useadmesh.com/agents" target="_blank" class="preview-link">
            Test on OpenGraph.xyz
        </a>
        <a href="https://developers.facebook.com/tools/debug/?q=https://www.useadmesh.com/agents" target="_blank" class="preview-link">
            Test on Facebook Debugger
        </a>
        <a href="https://cards-dev.twitter.com/validator?url=https://www.useadmesh.com/agents" target="_blank" class="preview-link">
            Test on Twitter Validator
        </a>
    </div>

    <div class="test-section">
        <h2>👥 Users Page</h2>
        <div class="test-url">https://www.useadmesh.com/users</div>
        <p><strong>Expected:</strong> Shows users content with users-og.png image</p>
        <a href="https://www.opengraph.xyz/?url=https://www.useadmesh.com/users" target="_blank" class="preview-link">
            Test on OpenGraph.xyz
        </a>
        <a href="https://developers.facebook.com/tools/debug/?q=https://www.useadmesh.com/users" target="_blank" class="preview-link">
            Test on Facebook Debugger
        </a>
        <a href="https://cards-dev.twitter.com/validator?url=https://www.useadmesh.com/users" target="_blank" class="preview-link">
            Test on Twitter Validator
        </a>
    </div>

    <div class="test-section">
        <h2>🎨 Dynamic OG Generation</h2>
        <div class="test-url">https://www.useadmesh.com/api/og?title=Custom%20Title&description=Custom%20Description</div>
        <p><strong>Expected:</strong> Generates custom OG image with provided title and description</p>
        <a href="https://www.opengraph.xyz/?url=https://www.useadmesh.com/api/og?title=Custom%20Title&description=Custom%20Description" target="_blank" class="preview-link">
            Test Dynamic OG
        </a>
    </div>

    <div class="test-section">
        <h2>📱 Social Media Platforms to Test</h2>
        <ul>
            <li><strong>Facebook:</strong> Share the URLs and check preview cards</li>
            <li><strong>Twitter/X:</strong> Tweet the URLs and verify Twitter Cards</li>
            <li><strong>LinkedIn:</strong> Post the URLs and check link previews</li>
            <li><strong>WhatsApp:</strong> Send the URLs and verify link previews</li>
            <li><strong>Discord:</strong> Share the URLs and check embeds</li>
            <li><strong>Slack:</strong> Share the URLs and verify unfurls</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>🔍 What to Verify</h2>
        <ul>
            <li>✅ <strong>Title:</strong> Correct page title appears</li>
            <li>✅ <strong>Description:</strong> Proper description text shows</li>
            <li>✅ <strong>Image:</strong> Correct OG image displays (1200x630px)</li>
            <li>✅ <strong>URL:</strong> Canonical URL is correct</li>
            <li>✅ <strong>Branding:</strong> AdMesh branding is consistent</li>
        </ul>
    </div>

    <div class="status success">
        🚀 <strong>Ready for Deployment:</strong> All OG functionality has been tested and is working correctly!
    </div>
</body>
</html>
