import { Metadata } from "next";
import { generateMetadata } from "./metadata";

// Home page specific metadata (showing Brands content)
// Since root redirects to /brands, we should set canonical to /brands
export const metadata: Metadata = generateMetadata(
  "For Brands", // Title
  "Connect with high-intent users looking for your products. AdMesh helps brands reach users at the perfect moment when they're actively searching for solutions.", // Description
  "/brands", // Path - use /brands as canonical since root redirects there
  undefined // Let the generateMetadata function create the static OG image URL
);
