import { Metadata } from "next";
import { generateMetadata } from "./metadata";

// Home page specific metadata (showing Brands content)
// Root page shows brands content but stays at root URL
export const metadata: Metadata = generateMetadata(
  "For Brands", // Title
  "Connect with high-intent users looking for your products. AdMesh helps brands reach users at the perfect moment when they're actively searching for solutions.", // Description
  "/", // Path - canonical points to root since that's where the content is served
  undefined // Let the generateMetadata function create the static OG image URL
);
