import { Metadata } from "next";
import { generateMetadata } from "../metadata";

// Agents page specific metadata
export const metadata: Metadata = generateMetadata(
  "For Agents",
  "Monetize Your AI Agent in Minutes. Whether you're building GPTs, Chrome extensions, or custom tools — AdMesh gives you plug-and-earn monetization with verified results.",
  "/agents",
  undefined // Let the generateMetadata function create the static OG image URL
);

export default function AgentsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
