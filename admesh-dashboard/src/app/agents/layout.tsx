import { Metadata } from "next";
import { generateMetadata } from "../metadata";

// Agents page specific metadata
export const metadata: Metadata = generateMetadata(
  "For Agents",
  "Join the AdMesh Agent Pioneer Program. Earn rewards by helping users discover the perfect tools and products for their needs.",
  "/agents",
  undefined // Let the generateMetadata function create the static OG image URL
);

export default function AgentsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
