import { Metadata } from "next";
import { generateMetadata } from "../metadata";

// Users page specific metadata
export const metadata: Metadata = generateMetadata(
  "For Users",
  "Discover AI tools, earn rewards, and power the future of monetized intent with AdMesh - Promote your products inside AI for finding the best tools and products.",
  "/users",
  undefined // Let the generateMetadata function create the static OG image URL
);

export default function UsersLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
